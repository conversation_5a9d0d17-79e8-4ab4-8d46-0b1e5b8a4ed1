import tarfile
import os
import shutil
import stat

def remove_readonly(func, path, _):
    """Clear the readonly bit and reattempt the removal"""
    os.chmod(path, stat.S_IWRITE)
    func(path)

# Remove existing directory if it exists
if os.path.exists("weworkapi_python"):
    print("Removing existing weworkapi_python directory...")
    shutil.rmtree("weworkapi_python", onerror=remove_readonly)

try:
    print("Extracting weworkapi_python.tar.bz2...")
    with tarfile.open("weworkapi_python.tar.bzip2", "r:bz2") as tar:
        tar.extractall()
    print("Extraction completed successfully!")
except PermissionError as e:
    print(f"Permission error: {e}")
    print("Try running as administrator or check if any files are in use.")
except Exception as e:
    print(f"Error during extraction: {e}")