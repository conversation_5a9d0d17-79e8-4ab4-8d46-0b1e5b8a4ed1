#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
企业微信消息处理器扩展
可以在这里添加自定义的消息处理逻辑
"""

import re
import json
import time
import logging
import xml.etree.ElementTree as ET
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class CustomMessageHandler:
    """自定义消息处理器"""
    
    def __init__(self):
        self.commands = {
            'help': self.handle_help_command,
            '帮助': self.handle_help_command,
            'time': self.handle_time_command,
            '时间': self.handle_time_command,
            'echo': self.handle_echo_command,
            '回声': self.handle_echo_command,
        }
    
    def handle_text_message(self, msg_data: str) -> Optional[str]:
        """处理文本消息"""
        try:
            xml_tree = ET.fromstring(msg_data)
            from_user = xml_tree.find("FromUserName").text
            content = xml_tree.find("Content").text.strip()
            msg_id = xml_tree.find("MsgId").text
            
            logger.info(f"处理文本消息 - 用户: {from_user}, 内容: {content}")
            
            # 检查是否是命令
            if content.startswith('/') or content.startswith('！'):
                return self.handle_command(from_user, content, msg_id)
            
            # 智能回复
            reply_content = self.generate_smart_reply(content)
            return self.create_text_reply(from_user, reply_content, msg_id)
            
        except Exception as e:
            logger.error(f"处理文本消息失败: {e}")
            return None
    
    def handle_command(self, from_user: str, content: str, msg_id: str) -> Optional[str]:
        """处理命令"""
        # 移除命令前缀
        command_text = content[1:].strip()
        
        # 解析命令和参数
        parts = command_text.split(' ', 1)
        command = parts[0].lower()
        args = parts[1] if len(parts) > 1 else ""
        
        # 执行命令
        if command in self.commands:
            try:
                reply_content = self.commands[command](args)
                return self.create_text_reply(from_user, reply_content, msg_id)
            except Exception as e:
                logger.error(f"执行命令失败: {e}")
                reply_content = f"命令执行失败: {str(e)}"
                return self.create_text_reply(from_user, reply_content, msg_id)
        else:
            reply_content = f"未知命令: {command}\n发送 /help 查看可用命令"
            return self.create_text_reply(from_user, reply_content, msg_id)
    
    def handle_help_command(self, args: str) -> str:
        """帮助命令"""
        help_text = """可用命令：
/help 或 /帮助 - 显示帮助信息
/time 或 /时间 - 显示当前时间
/echo <内容> 或 /回声 <内容> - 回声测试

示例：
/time
/echo 你好世界"""
        return help_text
    
    def handle_time_command(self, args: str) -> str:
        """时间命令"""
        current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        return f"当前时间: {current_time}"
    
    def handle_echo_command(self, args: str) -> str:
        """回声命令"""
        if args:
            return f"回声: {args}"
        else:
            return "请提供要回声的内容，例如: /echo 你好"
    
    def generate_smart_reply(self, content: str) -> str:
        """生成智能回复"""
        content_lower = content.lower()
        
        # 问候语
        if any(word in content_lower for word in ['你好', 'hello', 'hi', '早上好', '下午好', '晚上好']):
            return "你好！有什么可以帮助您的吗？"
        
        # 感谢
        if any(word in content_lower for word in ['谢谢', 'thanks', 'thank you', '感谢']):
            return "不客气！很高兴能帮助您。"
        
        # 询问时间
        if any(word in content_lower for word in ['时间', 'time', '几点', '现在']):
            current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            return f"当前时间是: {current_time}"
        
        # 默认回复
        return f"收到您的消息: {content}\n\n发送 /help 查看可用命令"
    
    def handle_event_message(self, msg_data: str) -> Optional[str]:
        """处理事件消息"""
        try:
            xml_tree = ET.fromstring(msg_data)
            from_user = xml_tree.find("FromUserName").text
            event = xml_tree.find("Event").text
            
            logger.info(f"处理事件消息 - 用户: {from_user}, 事件: {event}")
            
            if event == "subscribe":
                reply_content = """欢迎关注！🎉

我是企业微信智能助手，可以为您提供以下服务：

📝 发送 /help 查看所有命令
⏰ 发送 /time 查看当前时间
🔊 发送 /echo <内容> 进行回声测试

有任何问题都可以随时联系我！"""
                
            elif event == "unsubscribe":
                reply_content = "感谢您的使用，期待再次为您服务！"
                
            elif event == "click":
                # 处理菜单点击事件
                event_key = xml_tree.find("EventKey").text if xml_tree.find("EventKey") is not None else ""
                reply_content = self.handle_menu_click(event_key)
                
            else:
                reply_content = f"收到事件: {event}"
            
            return self.create_text_reply(from_user, reply_content)
            
        except Exception as e:
            logger.error(f"处理事件消息失败: {e}")
            return None
    
    def handle_menu_click(self, event_key: str) -> str:
        """处理菜单点击事件"""
        menu_handlers = {
            'MENU_HELP': '欢迎使用帮助功能！发送 /help 查看详细命令列表。',
            'MENU_TIME': f'当前时间: {time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())}',
            'MENU_ABOUT': '这是企业微信智能助手，基于Python Flask开发。'
        }
        
        return menu_handlers.get(event_key, f'点击了菜单: {event_key}')
    
    def create_text_reply(self, to_user: str, content: str, msg_id: str = None) -> str:
        """创建文本回复消息"""
        timestamp = str(int(time.time()))
        
        # 限制回复内容长度
        if len(content) > 2000:
            content = content[:1997] + "..."
        
        reply_xml = f"""<xml>
<ToUserName><![CDATA[{to_user}]]></ToUserName>
<FromUserName><![CDATA[企业微信助手]]></FromUserName>
<CreateTime>{timestamp}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[{content}]]></Content>
</xml>"""
        
        return reply_xml
    
    def create_news_reply(self, to_user: str, articles: list) -> str:
        """创建图文消息回复"""
        timestamp = str(int(time.time()))
        
        articles_xml = ""
        for article in articles[:8]:  # 最多8条图文
            articles_xml += f"""
<item>
<Title><![CDATA[{article.get('title', '')}]]></Title>
<Description><![CDATA[{article.get('description', '')}]]></Description>
<PicUrl><![CDATA[{article.get('pic_url', '')}]]></PicUrl>
<Url><![CDATA[{article.get('url', '')}]]></Url>
</item>"""
        
        reply_xml = f"""<xml>
<ToUserName><![CDATA[{to_user}]]></ToUserName>
<FromUserName><![CDATA[企业微信助手]]></FromUserName>
<CreateTime>{timestamp}</CreateTime>
<MsgType><![CDATA[news]]></MsgType>
<ArticleCount>{len(articles)}</ArticleCount>
<Articles>{articles_xml}
</Articles>
</xml>"""
        
        return reply_xml

# 创建全局处理器实例
custom_handler = CustomMessageHandler()
