#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
企业微信服务器监控脚本
用于监控服务器状态和处理常见问题
"""

import os
import sys
import time
import requests
import logging
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ServerMonitor:
    """服务器监控器"""
    
    def __init__(self, host='localhost', port=5000):
        self.host = host
        self.port = port
        self.base_url = f"http://{host}:{port}"
        
    def check_health(self):
        """检查服务器健康状态"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ 服务器健康 - 状态: {data.get('status')}, 服务: {data.get('service')}")
                return True
            else:
                logger.error(f"❌ 服务器响应异常 - 状态码: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            logger.error("❌ 无法连接到服务器")
            return False
        except requests.exceptions.Timeout:
            logger.error("❌ 服务器响应超时")
            return False
        except Exception as e:
            logger.error(f"❌ 健康检查失败: {e}")
            return False
    
    def check_config(self):
        """检查配置状态"""
        try:
            response = requests.get(f"{self.base_url}/config", timeout=5)
            if response.status_code == 200:
                data = response.json()
                logger.info(f"📋 配置状态:")
                logger.info(f"   - 企业ID: {data.get('corp_id')}")
                logger.info(f"   - Token配置: {'✅' if data.get('token_configured') else '❌'}")
                logger.info(f"   - AES密钥配置: {'✅' if data.get('aes_key_configured') else '❌'}")
                return data
            else:
                logger.error(f"❌ 获取配置失败 - 状态码: {response.status_code}")
                return None
        except Exception as e:
            logger.error(f"❌ 检查配置失败: {e}")
            return None
    
    def test_callback_url(self):
        """测试回调URL（仅GET请求，不会触发实际验证）"""
        try:
            # 只测试URL是否可访问，不传递验证参数
            response = requests.get(f"{self.base_url}/wecom/callback", timeout=5)
            # 期望返回422（缺少必需参数）或其他非500错误
            if response.status_code in [422, 400]:
                logger.info("✅ 回调URL可访问（缺少验证参数是正常的）")
                return True
            else:
                logger.warning(f"⚠️ 回调URL响应异常 - 状态码: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"❌ 测试回调URL失败: {e}")
            return False
    
    def monitor_continuous(self, interval=30):
        """持续监控"""
        logger.info(f"🔍 开始持续监控，检查间隔: {interval}秒")
        logger.info("按 Ctrl+C 停止监控")
        
        try:
            while True:
                logger.info(f"\n{'='*50}")
                logger.info(f"监控时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 健康检查
                health_ok = self.check_health()
                
                # 配置检查
                config_data = self.check_config()
                
                # 回调URL检查
                callback_ok = self.test_callback_url()
                
                # 总结状态
                if health_ok and config_data and callback_ok:
                    logger.info("🎉 所有检查通过")
                else:
                    logger.warning("⚠️ 发现问题，请检查上述错误信息")
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            logger.info("\n👋 监控已停止")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='企业微信服务器监控工具')
    parser.add_argument('--host', default='localhost', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=5000, help='服务器端口')
    parser.add_argument('--monitor', action='store_true', help='持续监控模式')
    parser.add_argument('--interval', type=int, default=30, help='监控间隔（秒）')
    
    args = parser.parse_args()
    
    monitor = ServerMonitor(args.host, args.port)
    
    if args.monitor:
        monitor.monitor_continuous(args.interval)
    else:
        # 单次检查
        logger.info("🔍 执行单次健康检查")
        monitor.check_health()
        monitor.check_config()
        monitor.test_callback_url()

if __name__ == '__main__':
    main()
