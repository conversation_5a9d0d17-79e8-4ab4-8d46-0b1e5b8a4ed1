#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
企业微信 Webhook 消息发送测试脚本
"""

import os
import sys
import requests
import json
import base64
import hashlib
from datetime import datetime

# 测试配置
TEST_CONFIG = {
    'server_url': 'http://localhost:5000'
}

def test_text_message():
    """测试发送文本消息"""
    print("测试发送文本消息...")
    try:
        data = {
            "content": f"这是一条测试文本消息 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "mentioned_list": ["@all"],  # @所有人
            "mentioned_mobile_list": []
        }
        
        response = requests.post(f"{TEST_CONFIG['server_url']}/webhook/send/text", json=data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 文本消息发送成功: {result}")
            return True
        else:
            print(f"❌ 文本消息发送失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 文本消息发送异常: {e}")
        return False

def test_markdown_message():
    """测试发送 Markdown 消息"""
    print("测试发送 Markdown 消息...")
    try:
        markdown_content = f"""# 企业微信 Webhook 测试
        
## 基本信息
- **时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **类型**: Markdown 消息测试
- **状态**: <font color="info">正常</font>

## 功能列表
1. 文本消息 ✅
2. Markdown 消息 ✅
3. 图片消息 🖼️
4. 图文消息 📰

> 这是一条测试 Markdown 消息

[查看更多](https://developer.work.weixin.qq.com/)"""

        data = {
            "content": markdown_content
        }
        
        response = requests.post(f"{TEST_CONFIG['server_url']}/webhook/send/markdown", json=data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Markdown 消息发送成功: {result}")
            return True
        else:
            print(f"❌ Markdown 消息发送失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Markdown 消息发送异常: {e}")
        return False

def test_image_message():
    """测试发送图片消息"""
    print("测试发送图片消息...")
    try:
        # 创建一个简单的测试图片（1x1像素的PNG）
        test_image_data = base64.b64decode(
            "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg=="
        )
        
        # 计算 base64 和 MD5
        image_base64 = base64.b64encode(test_image_data).decode('utf-8')
        image_md5 = hashlib.md5(test_image_data).hexdigest()
        
        data = {
            "base64": image_base64,
            "md5": image_md5
        }
        
        response = requests.post(f"{TEST_CONFIG['server_url']}/webhook/send/image", json=data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 图片消息发送成功: {result}")
            return True
        else:
            print(f"❌ 图片消息发送失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 图片消息发送异常: {e}")
        return False

def test_news_message():
    """测试发送图文消息"""
    print("测试发送图文消息...")
    try:
        data = {
            "articles": [
                {
                    "title": "企业微信 Webhook 测试",
                    "description": "这是一条测试图文消息，用于验证 Webhook 功能",
                    "url": "https://developer.work.weixin.qq.com/",
                    "picurl": "https://wwcdn.weixin.qq.com/node/wework/images/202201062104.366e5ee28e.png"
                },
                {
                    "title": "FastAPI 文档",
                    "description": "现代化的 Python Web 框架",
                    "url": "https://fastapi.tiangolo.com/",
                    "picurl": "https://fastapi.tiangolo.com/img/logo-margin/logo-teal.png"
                }
            ]
        }
        
        response = requests.post(f"{TEST_CONFIG['server_url']}/webhook/send/news", json=data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 图文消息发送成功: {result}")
            return True
        else:
            print(f"❌ 图文消息发送失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 图文消息发送异常: {e}")
        return False

def test_template_card_message():
    """测试发送模板卡片消息"""
    print("测试发送模板卡片消息...")
    try:
        data = {
            "card_type": "text_notice",
            "source": {
                "icon_url": "https://wwcdn.weixin.qq.com/node/wework/images/202201062104.366e5ee28e.png",
                "desc": "企业微信",
                "desc_color": 0
            },
            "main_title": {
                "title": "欢迎使用企业微信 Webhook",
                "desc": "这是一条模板卡片测试消息"
            },
            "emphasis_content": {
                "title": "重要提醒",
                "desc": "请确保 Webhook Key 配置正确"
            },
            "quote_area": {
                "type": 1,
                "url": "https://developer.work.weixin.qq.com/",
                "appid": "APPID",
                "pagepath": "PAGEPATH",
                "title": "查看详情",
                "quote_text": "点击查看企业微信开发文档"
            },
            "sub_title_text": f"发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "horizontal_content_list": [
                {
                    "keyname": "状态",
                    "value": "正常"
                },
                {
                    "keyname": "类型",
                    "value": "测试消息"
                }
            ],
            "jump_list": [
                {
                    "type": 1,
                    "url": "https://developer.work.weixin.qq.com/",
                    "title": "企业微信开发文档"
                }
            ],
            "card_action": {
                "type": 1,
                "url": "https://developer.work.weixin.qq.com/"
            }
        }
        
        response = requests.post(f"{TEST_CONFIG['server_url']}/webhook/send/template_card", json=data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 模板卡片消息发送成功: {result}")
            return True
        else:
            print(f"❌ 模板卡片消息发送失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 模板卡片消息发送异常: {e}")
        return False

def check_server_running():
    """检查服务器是否运行"""
    print("检查服务器状态...")
    try:
        response = requests.get(f"{TEST_CONFIG['server_url']}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("企业微信 Webhook 消息发送测试")
    print("=" * 60)
    
    # 检查服务器是否运行
    if not check_server_running():
        print("❌ 服务器未运行，请先启动服务器:")
        print("   python start_fastapi_server.py")
        return
    
    print("✅ 服务器正在运行")
    print()
    
    # 运行测试
    tests = [
        test_text_message,
        test_markdown_message,
        test_image_message,
        test_news_message,
        test_template_card_message
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
        print()
    
    # 测试结果
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有 Webhook 消息测试通过！")
    else:
        print("⚠️  部分测试失败，请检查配置")
    
    print()
    print("注意事项:")
    print("- 请确保在 .env 文件中设置了正确的 WECOM_WEBHOOK_KEY")
    print("- 文件和语音消息需要先上传媒体文件获取 media_id")
    print("- 图片消息需要提供正确的 base64 编码和 MD5 值")

if __name__ == '__main__':
    main()
