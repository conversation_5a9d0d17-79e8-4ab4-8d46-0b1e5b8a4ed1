#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业微信群聊自动回复测试脚本
"""

import requests
import json
import time
import xml.etree.ElementTree as ET

# 测试配置
TEST_CONFIG = {
    'server_url': 'http://localhost:3000',
    'test_group_id': 'test_group_123',
    'test_user_id': 'test_user_456'
}

def test_group_chat_xml():
    """测试群聊XML格式消息"""
    print("=" * 50)
    print("测试群聊XML格式消息")
    print("=" * 50)
    
    # 模拟群聊消息XML
    group_message_xml = f"""<xml>
<ToUserName><![CDATA[企业微信助手]]></ToUserName>
<FromUserName><![CDATA[{TEST_CONFIG['test_user_id']}]]></FromUserName>
<CreateTime>{int(time.time())}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[@机器人 你好，请问现在几点了？]]></Content>
<MsgId>1234567890</MsgId>
<ChatId><![CDATA[{TEST_CONFIG['test_group_id']}]]></ChatId>
</xml>"""
    
    print(f"发送群聊消息: {group_message_xml}")
    
    try:
        # 这里需要模拟企业微信的加密过程
        # 实际测试时需要使用正确的加密参数
        response = requests.post(
            f"{TEST_CONFIG['server_url']}/wecom/callback",
            data=group_message_xml.encode('utf-8'),
            headers={'Content-Type': 'application/xml; charset=utf-8'},
            params={
                'msg_signature': 'test_signature',
                'timestamp': str(int(time.time())),
                'nonce': 'test_nonce'
            }
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 群聊XML消息测试成功")
        else:
            print("❌ 群聊XML消息测试失败")
            
    except Exception as e:
        print(f"❌ 群聊XML消息测试异常: {e}")

def test_group_chat_json():
    """测试群聊JSON格式消息（智能机器人）"""
    print("=" * 50)
    print("测试群聊JSON格式消息（智能机器人）")
    print("=" * 50)
    
    # 模拟群聊JSON消息
    group_message_json = {
        "msgtype": "text",
        "from": {
            "userid": TEST_CONFIG['test_user_id']
        },
        "text": {
            "content": "@机器人 帮助"
        },
        "msgid": "test_msg_123",
        "chat_id": TEST_CONFIG['test_group_id']
    }
    
    print(f"发送群聊JSON消息: {json.dumps(group_message_json, ensure_ascii=False, indent=2)}")
    
    try:
        # 这里需要模拟企业微信的加密过程
        response = requests.post(
            f"{TEST_CONFIG['server_url']}/wecom/callback",
            data=json.dumps(group_message_json, ensure_ascii=False).encode('utf-8'),
            headers={'Content-Type': 'application/json; charset=utf-8'},
            params={
                'msg_signature': 'test_signature',
                'timestamp': str(int(time.time())),
                'nonce': 'test_nonce'
            }
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                response_json = json.loads(response.text)
                if response_json.get('msgtype') == 'stream':
                    print("✅ 群聊JSON消息测试成功")
                    print(f"回复内容: {response_json.get('stream', {}).get('content', '')}")
                else:
                    print("❌ 响应格式不正确")
            except:
                print("✅ 群聊JSON消息测试成功（非JSON响应）")
        else:
            print("❌ 群聊JSON消息测试失败")
            
    except Exception as e:
        print(f"❌ 群聊JSON消息测试异常: {e}")

def test_private_chat_comparison():
    """测试私聊消息对比"""
    print("=" * 50)
    print("测试私聊消息对比")
    print("=" * 50)
    
    # 模拟私聊消息XML（不包含ChatId）
    private_message_xml = f"""<xml>
<ToUserName><![CDATA[企业微信助手]]></ToUserName>
<FromUserName><![CDATA[{TEST_CONFIG['test_user_id']}]]></FromUserName>
<CreateTime>{int(time.time())}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[你好，请问现在几点了？]]></Content>
<MsgId>1234567891</MsgId>
</xml>"""
    
    print(f"发送私聊消息: {private_message_xml}")
    
    try:
        response = requests.post(
            f"{TEST_CONFIG['server_url']}/wecom/callback",
            data=private_message_xml.encode('utf-8'),
            headers={'Content-Type': 'application/xml; charset=utf-8'},
            params={
                'msg_signature': 'test_signature',
                'timestamp': str(int(time.time())),
                'nonce': 'test_nonce'
            }
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 私聊消息测试成功")
        else:
            print("❌ 私聊消息测试失败")
            
    except Exception as e:
        print(f"❌ 私聊消息测试异常: {e}")

def test_group_no_reply():
    """测试群聊中不需要回复的消息"""
    print("=" * 50)
    print("测试群聊中不需要回复的消息")
    print("=" * 50)
    
    # 模拟群聊中的普通聊天（不@机器人，不包含关键词）
    group_message_xml = f"""<xml>
<ToUserName><![CDATA[企业微信助手]]></ToUserName>
<FromUserName><![CDATA[{TEST_CONFIG['test_user_id']}]]></FromUserName>
<CreateTime>{int(time.time())}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[大家好，今天天气不错]]></Content>
<MsgId>1234567892</MsgId>
<ChatId><![CDATA[{TEST_CONFIG['test_group_id']}]]></ChatId>
</xml>"""
    
    print(f"发送群聊普通消息: {group_message_xml}")
    
    try:
        response = requests.post(
            f"{TEST_CONFIG['server_url']}/wecom/callback",
            data=group_message_xml.encode('utf-8'),
            headers={'Content-Type': 'application/xml; charset=utf-8'},
            params={
                'msg_signature': 'test_signature',
                'timestamp': str(int(time.time())),
                'nonce': 'test_nonce'
            }
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200 and (not response.text or response.text == "success"):
            print("✅ 群聊不回复测试成功（正确忽略了普通聊天）")
        else:
            print("❌ 群聊不回复测试失败（不应该回复普通聊天）")
            
    except Exception as e:
        print(f"❌ 群聊不回复测试异常: {e}")

def main():
    """主测试函数"""
    print("企业微信群聊自动回复功能测试")
    print("=" * 50)
    
    # 检查服务器状态
    try:
        response = requests.get(f"{TEST_CONFIG['server_url']}/health")
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print("❌ 服务器状态异常")
            return
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return
    
    # 运行测试
    test_private_chat_comparison()
    time.sleep(1)
    
    test_group_chat_xml()
    time.sleep(1)
    
    test_group_chat_json()
    time.sleep(1)
    
    test_group_no_reply()
    
    print("=" * 50)
    print("测试完成")

if __name__ == "__main__":
    main()
