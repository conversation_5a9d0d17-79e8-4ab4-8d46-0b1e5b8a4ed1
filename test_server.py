#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
企业微信回调服务测试脚本
"""

import os
import sys
import requests
import json
import time
from urllib.parse import urlencode

# 测试配置
TEST_CONFIG = {
    'server_url': 'http://localhost:5000',
    'token': 'test_token',
    'encoding_aes_key': 'test_encoding_aes_key_32_characters_long',
    'corp_id': 'test_corp_id'
}

def test_health_check():
    """测试健康检查接口"""
    print("测试健康检查接口...")
    try:
        response = requests.get(f"{TEST_CONFIG['server_url']}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查成功: {data}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_config_endpoint():
    """测试配置接口"""
    print("测试配置接口...")
    try:
        response = requests.get(f"{TEST_CONFIG['server_url']}/config")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 配置接口成功: {data}")
            return True
        else:
            print(f"❌ 配置接口失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 配置接口异常: {e}")
        return False

def test_url_verification():
    """测试URL验证（需要正确的配置）"""
    print("测试URL验证...")
    
    # 模拟企业微信的URL验证请求
    params = {
        'msg_signature': 'test_signature',
        'timestamp': str(int(time.time())),
        'nonce': 'test_nonce',
        'echostr': 'test_echostr'
    }
    
    try:
        url = f"{TEST_CONFIG['server_url']}/wecom/callback"
        response = requests.get(url, params=params)
        print(f"URL验证响应状态码: {response.status_code}")
        print(f"URL验证响应内容: {response.text}")
        
        # 由于没有正确的配置，预期会失败
        if response.status_code == 403:
            print("✅ URL验证按预期失败（需要正确配置）")
            return True
        else:
            print(f"⚠️  URL验证状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ URL验证异常: {e}")
        return False

def test_message_handling():
    """测试消息处理（需要正确的配置）"""
    print("测试消息处理...")
    
    # 模拟企业微信的消息推送
    params = {
        'msg_signature': 'test_signature',
        'timestamp': str(int(time.time())),
        'nonce': 'test_nonce'
    }
    
    # 模拟加密的XML消息
    test_xml = """<xml>
<ToUserName><![CDATA[test_corp]]></ToUserName>
<Encrypt><![CDATA[test_encrypted_content]]></Encrypt>
<AgentID><![CDATA[1000002]]></AgentID>
</xml>"""
    
    try:
        url = f"{TEST_CONFIG['server_url']}/wecom/callback"
        response = requests.post(
            url, 
            params=params,
            data=test_xml,
            headers={'Content-Type': 'application/xml'}
        )
        print(f"消息处理响应状态码: {response.status_code}")
        print(f"消息处理响应内容: {response.text}")
        
        # 由于没有正确的配置，预期会返回success
        if response.status_code == 200 and response.text == "success":
            print("✅ 消息处理按预期返回success")
            return True
        else:
            print(f"⚠️  消息处理状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 消息处理异常: {e}")
        return False

def check_server_running():
    """检查服务器是否运行"""
    print("检查服务器状态...")
    try:
        response = requests.get(f"{TEST_CONFIG['server_url']}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("企业微信回调服务测试")
    print("=" * 50)
    
    # 检查服务器是否运行
    if not check_server_running():
        print("❌ 服务器未运行，请先启动服务器:")
        print("   python start_server.py")
        print("   或")
        print("   python wecom_callback_server.py")
        return
    
    print("✅ 服务器正在运行")
    print()
    
    # 运行测试
    tests = [
        test_health_check,
        test_config_endpoint,
        test_url_verification,
        test_message_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
        print()
    
    # 测试结果
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查配置和服务状态")
    
    print()
    print("注意事项:")
    print("- URL验证和消息处理需要正确的企业微信配置")
    print("- 请在 .env 文件中设置正确的 TOKEN、ENCODING_AES_KEY 和 CORP_ID")
    print("- 生产环境请确保服务器可以被企业微信访问")

if __name__ == '__main__':
    main()
