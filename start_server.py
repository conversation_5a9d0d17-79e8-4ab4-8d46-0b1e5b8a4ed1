#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
企业微信回调服务启动脚本
"""

import os
import sys
import logging
from config import config,Config

def setup_logging(log_level='INFO', log_file=None):
    """设置日志"""
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    # 日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    # 根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    root_logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

def check_config():
    """检查配置"""
    env = os.getenv('FLASK_ENV', 'development')
    cfg = config.get(env, config['default'])
    print(env, cfg)
    required_configs = [
        ('WECOM_TOKEN', cfg.WECOM_TOKEN),
        ('WECOM_ENCODING_AES_KEY', cfg.WECOM_ENCODING_AES_KEY),
        ('WECOM_CORP_ID', cfg.WECOM_CORP_ID)
    ]
    print(required_configs)
    missing_configs = []
    for name, value in required_configs:
        if not value or value == f'your_{name.lower().replace("wecom_", "")}_here':
            missing_configs.append(name)
    
    if missing_configs:
        print(f"错误: 缺少必要的配置项: {', '.join(missing_configs)}")
        print("请设置环境变量或复制 .env.example 为 .env 并填写正确的配置")
        return False
    
    return True

def main():
    """主函数"""
    # 检查配置
    if not check_config():
        sys.exit(1)
    
    # 设置日志
    env = os.getenv('FLASK_ENV', 'development')
    cfg = config.get(env, config['default'])
    setup_logging(cfg.LOG_LEVEL, cfg.LOG_FILE)
    
    # 导入并启动应用
    from wecom_callback_server import app
    
    logger = logging.getLogger(__name__)
    logger.info(f"启动企业微信回调服务 - 环境: {env}")
    logger.info(f"服务地址: http://{cfg.HOST}:{cfg.PORT}")
    logger.info(f"回调地址: http://{cfg.HOST}:{cfg.PORT}/wecom/callback")
    
    try:
        app.run(
            host=cfg.HOST,
            port=cfg.PORT,
            debug=cfg.DEBUG
        )
    except KeyboardInterrupt:
        logger.info("服务已停止")
    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
