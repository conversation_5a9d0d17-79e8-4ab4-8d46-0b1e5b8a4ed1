#!/bin/bash

# 企业微信回调服务部署脚本

set -e

echo "开始部署企业微信回调服务..."

# 检查Python版本
echo "检查Python版本..."
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.6"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" = "$required_version" ]; then
    echo "✅ Python版本检查通过: $python_version"
else
    echo "❌ Python版本过低，需要3.6或更高版本"
    exit 1
fi

# 创建虚拟环境
if [ ! -d "venv" ]; then
    echo "创建虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# 升级pip
echo "升级pip..."
pip install --upgrade pip

# 安装依赖
echo "安装依赖..."
pip install -r requirements.txt

# 检查配置文件
if [ ! -f ".env" ]; then
    echo "创建配置文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件，填写正确的企业微信配置"
fi

# 检查加解密库
if [ ! -d "weworkapi_python" ]; then
    echo "❌ 缺少企业微信加解密库 weworkapi_python"
    echo "请确保 weworkapi_python 目录存在"
    exit 1
fi

echo "✅ 部署完成！"
echo ""
echo "下一步操作："
echo "1. 编辑 .env 文件，填写企业微信配置"
echo "2. 启动服务: python start_server.py"
echo "3. 测试服务: python test_server.py"
echo ""
echo "生产环境部署建议："
echo "- 使用 Gunicorn: gunicorn -w 4 -b 0.0.0.0:5000 wecom_callback_server:app"
echo "- 使用 Nginx 反向代理"
echo "- 配置 HTTPS 证书"
echo "- 设置防火墙规则"
