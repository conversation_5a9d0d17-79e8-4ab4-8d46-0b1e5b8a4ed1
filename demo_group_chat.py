#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业微信群聊自动回复功能演示
"""

import xml.etree.ElementTree as ET
from message_handlers import CustomMessageHandler

def demo_message_processing():
    """演示消息处理功能"""
    print("=" * 60)
    print("企业微信群聊自动回复功能演示")
    print("=" * 60)
    
    # 创建消息处理器
    handler = CustomMessageHandler()
    
    # 测试用例
    test_cases = [
        {
            "name": "私聊消息 - 问候",
            "xml": """<xml>
<ToUserName><![CDATA[企业微信助手]]></ToUserName>
<FromUserName><![CDATA[user123]]></FromUserName>
<CreateTime>1234567890</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[你好]]></Content>
<MsgId>1001</MsgId>
</xml>""",
            "expected": "私聊回复，不带@"
        },
        {
            "name": "群聊消息 - @机器人",
            "xml": """<xml>
<ToUserName><![CDATA[企业微信助手]]></ToUserName>
<FromUserName><![CDATA[user123]]></FromUserName>
<CreateTime>1234567890</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[@机器人 你好]]></Content>
<MsgId>1002</MsgId>
<ChatId><![CDATA[group456]]></ChatId>
</xml>""",
            "expected": "群聊回复，带@用户"
        },
        {
            "name": "群聊消息 - 关键词触发",
            "xml": """<xml>
<ToUserName><![CDATA[企业微信助手]]></ToUserName>
<FromUserName><![CDATA[user123]]></FromUserName>
<CreateTime>1234567890</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[帮助]]></Content>
<MsgId>1003</MsgId>
<ChatId><![CDATA[group456]]></ChatId>
</xml>""",
            "expected": "群聊回复，带@用户"
        },
        {
            "name": "群聊消息 - 普通聊天",
            "xml": """<xml>
<ToUserName><![CDATA[企业微信助手]]></ToUserName>
<FromUserName><![CDATA[user123]]></FromUserName>
<CreateTime>1234567890</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[大家好，今天天气不错]]></Content>
<MsgId>1004</MsgId>
<ChatId><![CDATA[group456]]></ChatId>
</xml>""",
            "expected": "不回复"
        },
        {
            "name": "群聊消息 - 命令",
            "xml": """<xml>
<ToUserName><![CDATA[企业微信助手]]></ToUserName>
<FromUserName><![CDATA[user123]]></FromUserName>
<CreateTime>1234567890</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[/time]]></Content>
<MsgId>1005</MsgId>
<ChatId><![CDATA[group456]]></ChatId>
</xml>""",
            "expected": "群聊回复，带@用户，显示时间"
        }
    ]
    
    # 执行测试
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 40)
        
        # 解析输入消息
        xml_tree = ET.fromstring(test_case['xml'])
        from_user = xml_tree.find("FromUserName").text
        content = xml_tree.find("Content").text
        chat_id = xml_tree.find("ChatId")
        is_group = chat_id is not None
        
        print(f"输入消息: {content}")
        print(f"消息类型: {'群聊' if is_group else '私聊'}")
        if is_group:
            print(f"群聊ID: {chat_id.text}")
        print(f"发送者: {from_user}")
        print(f"期望结果: {test_case['expected']}")
        
        # 处理消息
        reply = handler.handle_text_message(test_case['xml'])
        
        if reply:
            # 解析回复消息
            reply_tree = ET.fromstring(reply)
            reply_content = reply_tree.find("Content").text
            to_user = reply_tree.find("ToUserName")
            chat_id_reply = reply_tree.find("ChatId")
            
            print(f"实际回复: {reply_content}")
            
            if chat_id_reply is not None:
                print(f"回复类型: 群聊回复 (ChatId: {chat_id_reply.text})")
            else:
                print(f"回复类型: 私聊回复 (ToUser: {to_user.text})")
                
            # 检查是否符合预期
            if is_group and "@user123" in reply_content:
                print("✅ 群聊回复格式正确（包含@用户）")
            elif not is_group and "@" not in reply_content:
                print("✅ 私聊回复格式正确（不包含@）")
            elif is_group:
                print("⚠️  群聊回复格式可能有问题")
            else:
                print("✅ 回复格式正确")
        else:
            print("实际回复: 无回复")
            if "不回复" in test_case['expected']:
                print("✅ 正确地没有回复")
            else:
                print("❌ 应该有回复但没有回复")

def demo_trigger_logic():
    """演示触发逻辑"""
    print("\n" + "=" * 60)
    print("群聊触发逻辑演示")
    print("=" * 60)
    
    handler = CustomMessageHandler()
    
    test_messages = [
        "@机器人 你好",
        "@bot 帮助",
        "@助手 时间",
        "帮助",
        "help",
        "现在几点了",
        "天气怎么样",
        "今天天气怎么样",
        "查询",
        "请问时间",
        "/help",
        "！time",
        "大家好",
        "今天天气不错",
        "有人在吗？",
        "哈哈哈",
        "时间过得真快",
        "天气真好"
    ]
    
    print("测试各种消息是否会触发群聊回复：\n")
    
    for msg in test_messages:
        should_reply = handler.should_reply_in_group(msg)
        status = "✅ 会回复" if should_reply else "❌ 不回复"
        print(f"'{msg}' -> {status}")

def demo_reply_formatting():
    """演示回复格式化"""
    print("\n" + "=" * 60)
    print("回复格式化演示")
    print("=" * 60)
    
    handler = CustomMessageHandler()
    
    test_cases = [
        ("@机器人 你好", "user123"),
        ("@bot 帮助", "user456"),
        ("时间", "user789"),
        ("@助手 天气怎么样", "userABC")
    ]
    
    print("群聊回复格式化示例：\n")
    
    for content, user in test_cases:
        reply = handler.generate_group_reply(content, user)
        print(f"输入: '{content}' (用户: {user})")
        print(f"输出: '{reply}'")
        print()

if __name__ == "__main__":
    try:
        demo_message_processing()
        demo_trigger_logic()
        demo_reply_formatting()
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("=" * 60)
        print("\n群聊自动回复功能特点：")
        print("1. 自动识别群聊和私聊消息")
        print("2. 群聊中只在特定条件下回复（@机器人、关键词、命令）")
        print("3. 群聊回复自动@发送者")
        print("4. 私聊正常回复，不受影响")
        print("5. 避免群聊中的过度回复")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
