# 企业微信回调API服务

提供两个版本的企业微信消息接收和处理服务：
- **Flask版本**: 基于Flask框架的传统实现
- **FastAPI版本**: 基于FastAPI框架的现代化实现，支持自动API文档生成

## 功能特性

### 共同特性
- ✅ 企业微信回调URL验证
- ✅ 消息加解密处理
- ✅ 文本消息接收和回复
- ✅ 事件消息处理
- ✅ 健康检查接口
- ✅ 配置管理
- ✅ 日志记录

### FastAPI版本额外特性
- 🚀 自动生成API文档 (Swagger UI)
- 📊 ReDoc文档支持
- ⚡ 高性能异步处理
- 🔍 自动请求/响应验证
- 📝 类型提示支持
- 🔧 OpenAPI 3.0规范

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置企业微信参数

复制配置文件模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，填写企业微信配置：
```bash
# 企业微信配置
WECOM_TOKEN=your_token_here
WECOM_ENCODING_AES_KEY=your_encoding_aes_key_here
WECOM_CORP_ID=your_corp_id_here
WECOM_APP_SECRET=your_app_secret_here

# 企业微信 Webhook 配置（可选）
WECOM_WEBHOOK_KEY=your_webhook_key_here
```

### 3. 启动服务

#### Flask版本
```bash
python start_server.py
```

或者直接运行：
```bash
python wecom_callback_server.py
```

#### FastAPI版本（推荐）
```bash
python start_fastapi_server.py
```

或者直接运行：
```bash
python wecom_fastapi_server.py
```

或者使用uvicorn：
```bash
uvicorn wecom_fastapi_server:app --host 0.0.0.0 --port 5000 --reload
```

## 配置说明

### 企业微信配置

在企业微信管理后台的应用设置中，需要配置以下参数：

1. **Token**: 用于验证消息来源的令牌
2. **EncodingAESKey**: 消息加解密密钥
3. **CorpID**: 企业ID

### 回调URL设置

在企业微信后台设置回调URL为：
```
http://your-domain.com:5000/wecom/callback
```

## API接口

### FastAPI版本 - 自动文档
- **Swagger UI**: http://localhost:5000/docs
- **ReDoc**: http://localhost:5000/redoc
- **OpenAPI规范**: http://localhost:5000/openapi.json

### 接口列表

#### 回调接口
- `GET/POST /wecom/callback` - 企业微信回调接口
- `GET /health` - 健康检查
- `GET /config` - 配置信息

#### 企业微信API接口
- `GET /access_token` - 获取访问令牌
- `POST /group_list` - 获取客户群列表
- `GET /WW_verify_eNFsvK7nLEz9Gbb7.txt` - 域名验证文件

#### Webhook 消息发送接口
- `POST /webhook/send/text` - 发送文本消息
- `POST /webhook/send/markdown` - 发送 Markdown 消息
- `POST /webhook/send/image` - 发送图片消息
- `POST /webhook/send/news` - 发送图文消息
- `POST /webhook/send/file` - 发送文件消息
- `POST /webhook/send/voice` - 发送语音消息
- `POST /webhook/send/template_card` - 发送模板卡片消息

### 回调接口
- **URL**: `/wecom/callback`
- **方法**: GET (URL验证) / POST (消息处理)
- **说明**: 企业微信回调接口

### 健康检查
- **URL**: `/health`
- **方法**: GET
- **返回**: 服务状态信息

### 配置查看
- **URL**: `/config`
- **方法**: GET
- **返回**: 当前配置状态（不包含敏感信息）

## 测试服务

### 基础功能测试
```bash
python test_fastapi_server.py
```

### Webhook 功能测试
```bash
python test_webhook.py
```

### 查看API文档
访问 `http://localhost:5000/docs`

## 消息处理

### 支持的消息类型

1. **文本消息**: 自动回复收到的文本内容
2. **事件消息**: 处理关注/取消关注等事件

### 自定义消息处理

在 `WeComMessageHandler` 类中可以自定义消息处理逻辑：

```python
def handle_text_message(self, msg_data):
    # 解析消息
    xml_tree = ET.fromstring(msg_data)
    content = xml_tree.find("Content").text
    
    # 自定义处理逻辑
    reply_content = your_custom_logic(content)
    
    # 返回回复消息
    return self.create_text_reply(from_user, reply_content)
```

## 部署建议

### 开发环境
```bash
export FLASK_ENV=development
python start_server.py
```

### 生产环境
```bash
export FLASK_ENV=production
python start_server.py
```

建议使用 Gunicorn 或 uWSGI 等WSGI服务器部署：
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 wecom_callback_server:app
```

## 日志

服务会记录详细的日志信息，包括：
- URL验证过程
- 消息接收和处理
- 错误信息

日志文件位置：`wecom_callback.log`

## 故障排除

### 常见问题

1. **URL验证失败**
   - 检查Token、EncodingAESKey、CorpID是否正确
   - 确认服务器可以被企业微信访问

2. **消息解密失败**
   - 检查EncodingAESKey是否正确
   - 确认CorpID匹配

3. **依赖安装失败**
   - 确保Python版本兼容
   - 使用 `pip install pycryptodome` 替代 `pycrypto`

### 调试模式

启用调试模式查看详细信息：
```bash
export DEBUG=True
python start_server.py
```

## 许可证

MIT License
