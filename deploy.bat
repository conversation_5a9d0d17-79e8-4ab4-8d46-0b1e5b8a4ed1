@echo off
chcp 65001 >nul
echo 开始部署企业微信回调服务...

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    echo 请安装Python 3.6或更高版本
    pause
    exit /b 1
)

echo ✅ Python检查通过

REM 创建虚拟环境
if not exist "venv" (
    echo 创建虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境
echo 激活虚拟环境...
call venv\Scripts\activate.bat

REM 升级pip
echo 升级pip...
python -m pip install --upgrade pip

REM 安装依赖
echo 安装依赖...
pip install -r requirements.txt

REM 检查配置文件
if not exist ".env" (
    echo 创建配置文件...
    copy .env.example .env
    echo ⚠️  请编辑 .env 文件，填写正确的企业微信配置
)

REM 检查加解密库
if not exist "weworkapi_python" (
    echo ❌ 缺少企业微信加解密库 weworkapi_python
    echo 请确保 weworkapi_python 目录存在
    pause
    exit /b 1
)

echo ✅ 部署完成！
echo.
echo 下一步操作：
echo 1. 编辑 .env 文件，填写企业微信配置
echo 2. 启动服务: python start_server.py
echo 3. 测试服务: python test_server.py
echo.
echo 生产环境部署建议：
echo - 使用 Gunicorn: pip install gunicorn ^&^& gunicorn -w 4 -b 0.0.0.0:5000 wecom_callback_server:app
echo - 使用 Nginx 反向代理
echo - 配置 HTTPS 证书
echo - 设置防火墙规则

pause
