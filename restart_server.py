#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
企业微信服务器重启脚本
用于安全地停止和重启服务器
"""

import os
import sys
import time
import signal
import psutil
import logging
import subprocess
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def find_server_processes():
    """查找正在运行的服务器进程"""
    processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
            if any(keyword in cmdline for keyword in ['wecom_fastapi_server', 'uvicorn', 'start_fastapi_server']):
                processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return processes

def stop_server_processes():
    """停止服务器进程"""
    processes = find_server_processes()
    
    if not processes:
        logger.info("没有找到正在运行的服务器进程")
        return True
    
    logger.info(f"找到 {len(processes)} 个服务器进程")
    
    # 首先尝试优雅停止
    for proc in processes:
        try:
            logger.info(f"正在停止进程 {proc.pid}: {proc.name()}")
            proc.terminate()
        except psutil.NoSuchProcess:
            continue
        except Exception as e:
            logger.error(f"停止进程 {proc.pid} 失败: {e}")
    
    # 等待进程停止
    time.sleep(3)
    
    # 检查是否还有进程在运行
    remaining_processes = find_server_processes()
    
    if remaining_processes:
        logger.warning("仍有进程在运行，强制终止...")
        for proc in remaining_processes:
            try:
                proc.kill()
                logger.info(f"强制终止进程 {proc.pid}")
            except psutil.NoSuchProcess:
                continue
            except Exception as e:
                logger.error(f"强制终止进程 {proc.pid} 失败: {e}")
        
        time.sleep(1)
    
    # 最终检查
    final_processes = find_server_processes()
    if final_processes:
        logger.error("无法停止所有服务器进程")
        return False
    else:
        logger.info("✅ 所有服务器进程已停止")
        return True

def start_server():
    """启动服务器"""
    try:
        logger.info("正在启动服务器...")
        
        # 检查启动脚本是否存在
        if os.path.exists("start_fastapi_server.py"):
            cmd = [sys.executable, "start_fastapi_server.py"]
        elif os.path.exists("wecom_fastapi_server.py"):
            cmd = [sys.executable, "wecom_fastapi_server.py"]
        else:
            logger.error("找不到服务器启动脚本")
            return False
        
        # 启动服务器
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        logger.info(f"✅ 服务器已启动，进程ID: {process.pid}")
        
        # 等待一下确保启动成功
        time.sleep(2)
        
        if process.poll() is None:
            logger.info("✅ 服务器启动成功")
            return True
        else:
            stdout, stderr = process.communicate()
            logger.error(f"服务器启动失败:")
            logger.error(f"stdout: {stdout}")
            logger.error(f"stderr: {stderr}")
            return False
            
    except Exception as e:
        logger.error(f"启动服务器失败: {e}")
        return False

def restart_server():
    """重启服务器"""
    logger.info("🔄 开始重启服务器...")
    
    # 停止现有进程
    if not stop_server_processes():
        logger.error("❌ 停止服务器失败")
        return False
    
    # 等待一下
    time.sleep(1)
    
    # 启动新进程
    if not start_server():
        logger.error("❌ 启动服务器失败")
        return False
    
    logger.info("🎉 服务器重启成功")
    return True

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='企业微信服务器重启工具')
    parser.add_argument('action', choices=['stop', 'start', 'restart', 'status'], 
                       help='执行的操作')
    
    args = parser.parse_args()
    
    if args.action == 'status':
        processes = find_server_processes()
        if processes:
            logger.info(f"找到 {len(processes)} 个服务器进程:")
            for proc in processes:
                logger.info(f"  - PID: {proc.pid}, 名称: {proc.name()}")
        else:
            logger.info("没有找到正在运行的服务器进程")
    
    elif args.action == 'stop':
        stop_server_processes()
    
    elif args.action == 'start':
        start_server()
    
    elif args.action == 'restart':
        restart_server()

if __name__ == '__main__':
    main()
