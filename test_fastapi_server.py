#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
企业微信回调服务测试脚本 - FastAPI版本
"""

import os
import sys
import requests
import json
import time
from urllib.parse import urlencode

# 测试配置
TEST_CONFIG = {
    'server_url': 'http://localhost:5000',
    'token': 'test_token',
    'encoding_aes_key': 'test_encoding_aes_key_32_characters_long',
    'corp_id': 'test_corp_id'
}

def test_root_endpoint():
    """测试根路径"""
    print("测试根路径...")
    try:
        response = requests.get(f"{TEST_CONFIG['server_url']}/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 根路径成功: {data}")
            return True
        else:
            print(f"❌ 根路径失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 根路径异常: {e}")
        return False

def test_health_check():
    """测试健康检查接口"""
    print("测试健康检查接口...")
    try:
        response = requests.get(f"{TEST_CONFIG['server_url']}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查成功: {data}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_config_endpoint():
    """测试配置接口"""
    print("测试配置接口...")
    try:
        response = requests.get(f"{TEST_CONFIG['server_url']}/config")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 配置接口成功: {data}")
            return True
        else:
            print(f"❌ 配置接口失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 配置接口异常: {e}")
        return False

def test_docs_endpoint():
    """测试API文档接口"""
    print("测试API文档接口...")
    try:
        response = requests.get(f"{TEST_CONFIG['server_url']}/docs")
        if response.status_code == 200:
            print(f"✅ API文档可访问")
            return True
        else:
            print(f"❌ API文档失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API文档异常: {e}")
        return False

def test_openapi_endpoint():
    """测试OpenAPI规范接口"""
    print("测试OpenAPI规范接口...")
    try:
        response = requests.get(f"{TEST_CONFIG['server_url']}/openapi.json")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ OpenAPI规范成功: {data.get('info', {}).get('title', 'Unknown')}")
            return True
        else:
            print(f"❌ OpenAPI规范失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ OpenAPI规范异常: {e}")
        return False

def test_access_token_endpoint():
    """测试获取访问令牌接口"""
    print("测试获取访问令牌接口...")
    try:
        response = requests.get(f"{TEST_CONFIG['server_url']}/access_token")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取访问令牌成功: {data.get('access_token', 'N/A')[:20]}...")
            return True
        else:
            print(f"❌ 获取访问令牌失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取访问令牌异常: {e}")
        return False

def test_group_list_endpoint():
    """测试获取群聊列表接口"""
    print("测试获取群聊列表接口...")
    try:
        data = {
            "limit": 10,
            "cursor": None,
            "status_filter": 0
        }
        response = requests.post(f"{TEST_CONFIG['server_url']}/group_list", json=data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 获取群聊列表成功: {result.get('errcode', 'N/A')}")
            return True
        else:
            print(f"❌ 获取群聊列表失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取群聊列表异常: {e}")
        return False

def test_verify_file_endpoint():
    """测试企业微信验证文件接口"""
    print("测试企业微信验证文件接口...")
    try:
        response = requests.get(f"{TEST_CONFIG['server_url']}/WW_verify_eNFsvK7nLEz9Gbb7.txt")
        if response.status_code == 200:
            content = response.text
            print(f"✅ 验证文件访问成功: {content}")
            return True
        else:
            print(f"❌ 验证文件访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 验证文件访问异常: {e}")
        return False

def test_webhook_text_message():
    """测试 Webhook 文本消息接口"""
    print("测试 Webhook 文本消息接口...")
    try:
        data = {
            "content": "这是一条测试文本消息",
            "mentioned_list": ["@all"]
        }
        response = requests.post(f"{TEST_CONFIG['server_url']}/webhook/send/text", json=data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Webhook 文本消息接口测试成功: {result.get('errcode', 'N/A')}")
            return True
        else:
            print(f"❌ Webhook 文本消息接口测试失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Webhook 文本消息接口测试异常: {e}")
        return False

def test_url_verification():
    """测试URL验证（需要正确的配置）"""
    print("测试URL验证...")
    
    # 模拟企业微信的URL验证请求
    params = {
        'msg_signature': 'test_signature',
        'timestamp': str(int(time.time())),
        'nonce': 'test_nonce',
        'echostr': 'test_echostr'
    }
    
    try:
        url = f"{TEST_CONFIG['server_url']}/wecom/callback"
        response = requests.get(url, params=params)
        print(f"URL验证响应状态码: {response.status_code}")
        print(f"URL验证响应内容: {response.text}")
        
        # 由于没有正确的配置，预期会失败
        if response.status_code == 403:
            print("✅ URL验证按预期失败（需要正确配置）")
            return True
        else:
            print(f"⚠️  URL验证状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ URL验证异常: {e}")
        return False

def test_message_handling():
    """测试消息处理（需要正确的配置）"""
    print("测试消息处理...")
    
    # 模拟企业微信的消息推送
    params = {
        'msg_signature': 'test_signature',
        'timestamp': str(int(time.time())),
        'nonce': 'test_nonce'
    }
    
    # 模拟加密的XML消息
    test_xml = """<xml>
<ToUserName><![CDATA[test_corp]]></ToUserName>
<Encrypt><![CDATA[test_encrypted_content]]></Encrypt>
<AgentID><![CDATA[1000002]]></AgentID>
</xml>"""
    
    try:
        url = f"{TEST_CONFIG['server_url']}/wecom/callback"
        response = requests.post(
            url, 
            params=params,
            data=test_xml,
            headers={'Content-Type': 'application/xml'}
        )
        print(f"消息处理响应状态码: {response.status_code}")
        print(f"消息处理响应内容: {response.text}")
        
        # 由于没有正确的配置，预期会返回success
        if response.status_code == 200 and response.text == "success":
            print("✅ 消息处理按预期返回success")
            return True
        else:
            print(f"⚠️  消息处理状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 消息处理异常: {e}")
        return False

def check_server_running():
    """检查服务器是否运行"""
    print("检查服务器状态...")
    try:
        response = requests.get(f"{TEST_CONFIG['server_url']}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("企业微信回调服务测试 - FastAPI版本")
    print("=" * 60)
    
    # 检查服务器是否运行
    if not check_server_running():
        print("❌ 服务器未运行，请先启动服务器:")
        print("   python start_fastapi_server.py")
        print("   或")
        print("   python wecom_fastapi_server.py")
        print("   或")
        print("   uvicorn wecom_fastapi_server:app --host 0.0.0.0 --port 5000")
        return
    
    print("✅ 服务器正在运行")
    print()
    
    # 运行测试
    tests = [
        test_root_endpoint,
        test_health_check,
        test_config_endpoint,
        test_docs_endpoint,
        test_openapi_endpoint,
        test_access_token_endpoint,
        test_group_list_endpoint,
        test_verify_file_endpoint,
        test_webhook_text_message,
        test_url_verification,
        test_message_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
        print()
    
    # 测试结果
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查配置和服务状态")
    
    print()
    print("FastAPI 特性:")
    print("- 🚀 自动生成API文档: http://localhost:5000/docs")
    print("- 📊 ReDoc文档: http://localhost:5000/redoc")
    print("- 🔧 OpenAPI规范: http://localhost:5000/openapi.json")
    print("- ⚡ 高性能异步处理")
    print("- 🔍 自动数据验证")
    print()
    print("注意事项:")
    print("- URL验证和消息处理需要正确的企业微信配置")
    print("- 请在 .env 文件中设置正确的 TOKEN、ENCODING_AES_KEY 和 CORP_ID")
    print("- 生产环境请确保服务器可以被企业微信访问")

if __name__ == '__main__':
    main()
