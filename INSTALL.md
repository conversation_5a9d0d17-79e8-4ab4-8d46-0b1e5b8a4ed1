# 企业微信回调API服务安装指南

## 系统要求

- Python 3.6 或更高版本
- pip 包管理器
- 企业微信管理员权限

## 快速安装

### Windows 系统

1. **运行部署脚本**
   ```cmd
   deploy.bat
   ```

2. **配置企业微信参数**
   ```cmd
   notepad .env
   ```
   填写以下配置：
   ```
   WECOM_TOKEN=your_token_here
   WECOM_ENCODING_AES_KEY=your_encoding_aes_key_here
   WECOM_CORP_ID=your_corp_id_here
   ```

3. **启动服务**
   ```cmd
   python start_server.py
   ```

### Linux/macOS 系统

1. **运行部署脚本**
   ```bash
   chmod +x deploy.sh
   ./deploy.sh
   ```

2. **配置企业微信参数**
   ```bash
   nano .env
   ```
   填写配置信息（同上）

3. **启动服务**
   ```bash
   python start_server.py
   ```

## 手动安装

### 1. 克隆或下载项目

确保项目目录包含以下文件：
- `wecom_callback_server.py` - 主服务文件
- `weworkapi_python/` - 企业微信加解密库
- `requirements.txt` - 依赖列表
- `.env.example` - 配置模板

### 2. 安装Python依赖

```bash
pip install -r requirements.txt
```

### 3. 配置环境变量

复制配置模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件：
```bash
# 企业微信配置
WECOM_TOKEN=your_token_here
WECOM_ENCODING_AES_KEY=your_encoding_aes_key_here
WECOM_CORP_ID=your_corp_id_here

# 服务配置
HOST=0.0.0.0
PORT=5000
DEBUG=False
```

### 4. 获取企业微信配置参数

#### 4.1 获取 CorpID
1. 登录企业微信管理后台
2. 进入"我的企业" -> "企业信息"
3. 复制"企业ID"

#### 4.2 创建自建应用
1. 进入"应用管理" -> "自建"
2. 点击"创建应用"
3. 填写应用信息并创建

#### 4.3 配置API接收消息
1. 进入应用详情页
2. 点击"API接收消息"
3. 设置以下参数：
   - **URL**: `http://your-domain.com:5000/wecom/callback`
   - **Token**: 随机生成或自定义（记录到.env文件）
   - **EncodingAESKey**: 随机生成（记录到.env文件）

### 5. 启动服务

```bash
python start_server.py
```

### 6. 验证配置

在企业微信后台点击"保存"按钮，系统会自动验证URL。

## 测试服务

运行测试脚本：
```bash
python test_server.py
```

## 生产环境部署

### 使用 Gunicorn

1. **安装 Gunicorn**
   ```bash
   pip install gunicorn
   ```

2. **启动服务**
   ```bash
   gunicorn -w 4 -b 0.0.0.0:5000 wecom_callback_server:app
   ```

### 使用 Nginx 反向代理

1. **安装 Nginx**
   ```bash
   sudo apt install nginx  # Ubuntu/Debian
   sudo yum install nginx  # CentOS/RHEL
   ```

2. **配置 Nginx**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           proxy_pass http://127.0.0.1:5000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       }
   }
   ```

### 配置 HTTPS

1. **获取SSL证书**（推荐使用 Let's Encrypt）
   ```bash
   sudo certbot --nginx -d your-domain.com
   ```

2. **更新企业微信回调URL**
   ```
   https://your-domain.com/wecom/callback
   ```

## 常见问题

### Q: URL验证失败
**A:** 检查以下项目：
- Token、EncodingAESKey、CorpID 是否正确
- 服务器是否可以被企业微信访问
- 防火墙是否开放对应端口

### Q: 消息解密失败
**A:** 检查：
- EncodingAESKey 是否正确
- CorpID 是否匹配

### Q: 依赖安装失败
**A:** 尝试：
- 升级 pip: `pip install --upgrade pip`
- 使用国内镜像: `pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt`

### Q: 服务无法启动
**A:** 检查：
- Python 版本是否 >= 3.6
- 端口是否被占用
- 配置文件是否正确

## 自定义开发

### 添加自定义消息处理

编辑 `message_handlers.py` 文件，添加自定义处理逻辑：

```python
def handle_text_message(self, msg_data: str) -> Optional[str]:
    # 解析消息
    xml_tree = ET.fromstring(msg_data)
    content = xml_tree.find("Content").text
    
    # 自定义处理逻辑
    if "天气" in content:
        reply_content = "今天天气不错！"
    else:
        reply_content = f"收到消息: {content}"
    
    # 返回回复
    return self.create_text_reply(from_user, reply_content)
```

### 集成外部API

可以在消息处理函数中调用外部API：

```python
import requests

def call_ai_api(text):
    response = requests.post('https://api.example.com/ai', {
        'text': text
    })
    return response.json()['reply']
```

## 技术支持

如有问题，请检查：
1. 日志文件 `wecom_callback.log`
2. 控制台输出信息
3. 企业微信后台的错误提示
