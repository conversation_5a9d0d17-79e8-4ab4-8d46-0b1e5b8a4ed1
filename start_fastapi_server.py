#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
企业微信回调服务启动脚本 - FastAPI版本
"""

import os
import sys
import logging
import uvicorn
from dotenv import load_dotenv

# 加载 .env 文件
load_dotenv()

def setup_logging(log_level='INFO', log_file=None):
    """设置日志"""
    level = getattr(logging, log_level.upper(), logging.INFO)

    # 日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)

    # 根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    root_logger.addHandler(console_handler)

    # 减少uvicorn的日志噪音
    uvicorn_logger = logging.getLogger("uvicorn")
    uvicorn_logger.setLevel(logging.WARNING)

    uvicorn_access_logger = logging.getLogger("uvicorn.access")
    uvicorn_access_logger.setLevel(logging.WARNING)

    uvicorn_error_logger = logging.getLogger("uvicorn.error")
    uvicorn_error_logger.setLevel(logging.WARNING)

    # 文件处理器
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

def check_config():
    """检查配置"""
    required_configs = [
        ('WECOM_TOKEN', os.getenv('WECOM_TOKEN')),
        ('WECOM_ENCODING_AES_KEY', os.getenv('WECOM_ENCODING_AES_KEY')),
        ('WECOM_CORP_ID', os.getenv('WECOM_CORP_ID'))
    ]
    
    missing_configs = []
    for name, value in required_configs:
        if not value or value == f'your_{name.lower().replace("wecom_", "")}_here':
            missing_configs.append(name)
    
    if missing_configs:
        print(f"错误: 缺少必要的配置项: {', '.join(missing_configs)}")
        print("请设置环境变量或复制 .env.example 为 .env 并填写正确的配置")
        return False
    
    return True

def main():
    """主函数"""
    # 检查配置
    if not check_config():
        sys.exit(1)
    
    # 设置日志
    log_level = os.getenv('LOG_LEVEL', 'INFO')
    log_file = os.getenv('LOG_FILE', 'wecom_callback.log')
    setup_logging(log_level, log_file)
    
    # 获取配置
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('DEBUG', 'False').lower() == 'true'
    
    logger = logging.getLogger(__name__)
    logger.info(f"启动企业微信回调服务（FastAPI版本）")
    logger.info(f"服务地址: http://{host}:{port}")
    logger.info(f"回调地址: http://{host}:{port}/wecom/callback")
    logger.info(f"API文档: http://{host}:{port}/docs")
    logger.info(f"调试模式: {debug}")
    
    try:
        uvicorn.run(
            "wecom_fastapi_server:app",
            host=host,
            port=port,
            reload=debug,
            log_level="warning",  # 减少日志噪音
            access_log=False      # 禁用访问日志以减少无效请求的日志
        )
    except KeyboardInterrupt:
        logger.info("服务已停止")
    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
