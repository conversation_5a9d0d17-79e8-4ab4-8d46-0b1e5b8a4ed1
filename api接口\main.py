import requests
import time

APPID = 'ww62cafe12f4ad5cff'  # 替换为您的企业微信AppID
APPSECRET = 'tXO7PS7EdjIyjPGAZOHF8qkw5URgWa8GjwG9m70IUsw'  # 替换为您的企业微信AppSecret

def get_access_token():
    url = f'https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={APPID}&corpsecret={APPSECRET}'
    response = requests.get(url)
    data = response.json()
    if 'access_token' in data:
        return data['access_token']
    else:
        raise Exception('获取ACCESS_TOKEN失败')


# 获取群聊列表
def get_group_list(access_token, limit=100, cursor=None, status_filter=0):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/list?access_token={access_token}"
    data = {
        "limit": limit,
        "cursor": cursor,
        "status_filter": status_filter
    }
    response = requests.post(url, json=data)
    return response.json()

# 获取群聊详情
def get_group_detail(access_token, chat_id, need_name=1):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/get?access_token={access_token}"
    data = {
        "chat_id": chat_id,
        "need_name": need_name
    }
    response = requests.post(url, json=data)
    return response.json()
#发送消息
def send_text_message(access_token, agentid, userid, content):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={access_token}"
    data = {
        "touser": userid,
        "msgtype": "text",
        "agentid": agentid,
        "text": {
            "content": content
        },
        "safe": 0
    }
    response = requests.post(url, json=data)
    return response.json()