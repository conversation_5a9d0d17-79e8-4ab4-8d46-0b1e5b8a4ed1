# 企业微信 Webhook 使用示例

本文档提供了企业微信 Webhook 各种消息类型的使用示例。

## 配置说明

在使用 Webhook 功能之前，请确保在 `.env` 文件中配置了正确的 Webhook Key：

```bash
WECOM_WEBHOOK_KEY=your_actual_webhook_key_here
```

## 消息类型示例

### 1. 文本消息

发送简单的文本消息，支持 @特定用户或@所有人。

```bash
curl -X POST http://localhost:5000/webhook/send/text \
  -H "Content-Type: application/json" \
  -d '{
    "content": "这是一条测试文本消息",
    "mentioned_list": ["@all"],
    "mentioned_mobile_list": ["13800138000"]
  }'
```

### 2. Markdown 消息

发送支持 Markdown 格式的富文本消息。

```bash
curl -X POST http://localhost:5000/webhook/send/markdown \
  -H "Content-Type: application/json" \
  -d '{
    "content": "# 系统通知\n\n## 服务状态\n- **状态**: <font color=\"info\">正常</font>\n- **时间**: 2024-01-01 12:00:00\n\n> 系统运行正常"
  }'
```

### 3. 图片消息

发送图片消息，需要提供图片的 base64 编码和 MD5 值。

```bash
curl -X POST http://localhost:5000/webhook/send/image \
  -H "Content-Type: application/json" \
  -d '{
    "base64": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==",
    "md5": "ad5b2e5eb2d8c4a3c5c8e5b2e5eb2d8c"
  }'
```

### 4. 图文消息

发送图文消息，支持多条图文（最多8条）。

```bash
curl -X POST http://localhost:5000/webhook/send/news \
  -H "Content-Type: application/json" \
  -d '{
    "articles": [
      {
        "title": "企业微信开发指南",
        "description": "了解如何使用企业微信 API 开发应用",
        "url": "https://developer.work.weixin.qq.com/",
        "picurl": "https://wwcdn.weixin.qq.com/node/wework/images/202201062104.366e5ee28e.png"
      }
    ]
  }'
```

### 5. 文件消息

发送文件消息，需要先上传文件获取 media_id。

```bash
curl -X POST http://localhost:5000/webhook/send/file \
  -H "Content-Type: application/json" \
  -d '{
    "media_id": "your_file_media_id"
  }'
```

### 6. 语音消息

发送语音消息，需要先上传语音文件获取 media_id。

```bash
curl -X POST http://localhost:5000/webhook/send/voice \
  -H "Content-Type: application/json" \
  -d '{
    "media_id": "your_voice_media_id"
  }'
```

### 7. 模板卡片消息

发送模板卡片消息，支持丰富的卡片样式。

```bash
curl -X POST http://localhost:5000/webhook/send/template_card \
  -H "Content-Type: application/json" \
  -d '{
    "card_type": "text_notice",
    "source": {
      "icon_url": "https://wwcdn.weixin.qq.com/node/wework/images/202201062104.366e5ee28e.png",
      "desc": "企业微信",
      "desc_color": 0
    },
    "main_title": {
      "title": "重要通知",
      "desc": "系统维护通知"
    },
    "emphasis_content": {
      "title": "维护时间",
      "desc": "2024-01-01 02:00-04:00"
    },
    "sub_title_text": "请提前做好准备",
    "horizontal_content_list": [
      {
        "keyname": "影响范围",
        "value": "全部服务"
      },
      {
        "keyname": "预计时长",
        "value": "2小时"
      }
    ]
  }'
```

## Python 代码示例

### 使用 requests 库发送消息

```python
import requests
import json

def send_text_message(content, mentioned_list=None):
    """发送文本消息"""
    url = "http://localhost:5000/webhook/send/text"
    data = {
        "content": content,
        "mentioned_list": mentioned_list or []
    }
    
    response = requests.post(url, json=data)
    return response.json()

def send_markdown_message(content):
    """发送 Markdown 消息"""
    url = "http://localhost:5000/webhook/send/markdown"
    data = {
        "content": content
    }
    
    response = requests.post(url, json=data)
    return response.json()

# 使用示例
if __name__ == "__main__":
    # 发送文本消息
    result = send_text_message("Hello, World!", ["@all"])
    print(f"文本消息发送结果: {result}")
    
    # 发送 Markdown 消息
    markdown_content = """
# 系统状态报告

## 服务状态
- **API 服务**: ✅ 正常
- **数据库**: ✅ 正常
- **缓存**: ⚠️ 警告

> 最后更新: 2024-01-01 12:00:00
"""
    result = send_markdown_message(markdown_content)
    print(f"Markdown 消息发送结果: {result}")
```

## 测试脚本

项目提供了专门的测试脚本来验证 Webhook 功能：

```bash
# 运行 Webhook 测试
python test_webhook.py

# 运行完整的服务测试（包含 Webhook 测试）
python test_fastapi_server.py
```

## 注意事项

1. **Webhook Key 配置**: 确保在企业微信后台获取正确的 Webhook Key
2. **消息频率限制**: 企业微信对 Webhook 消息有频率限制，请合理使用
3. **媒体文件**: 文件和语音消息需要先通过企业微信 API 上传获取 media_id
4. **图片格式**: 图片消息需要提供正确的 base64 编码和 MD5 值
5. **卡片模板**: 模板卡片消息的字段较多，请参考企业微信官方文档

## 错误处理

API 会返回标准的错误响应：

```json
{
  "detail": "错误描述",
  "status_code": 400
}
```

常见错误：
- `Webhook Key 未配置`: 请检查 `.env` 文件中的配置
- `发送失败`: 检查 Webhook Key 是否正确，消息格式是否符合要求
- `网络请求失败`: 检查网络连接和企业微信服务状态

## 更多信息

- [企业微信 Webhook 官方文档](https://developer.work.weixin.qq.com/document/path/91770)
- [FastAPI 文档](https://fastapi.tiangolo.com/)
- [项目 API 文档](http://localhost:5000/docs)
