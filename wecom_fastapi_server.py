#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
企业微信回调API服务 - FastAPI版本
基于FastAPI框架实现企业微信消息接收和处理
"""

import os
import sys
import json
import time
import logging
import requests
from typing import Optional, List
from dotenv import load_dotenv
from fastapi import FastAPI, Request, HTTPException, Query, WebSocket
from fastapi.responses import PlainTextResponse, JSONResponse, FileResponse
from fastapi.openapi.docs import get_swagger_ui_html
from starlette.staticfiles import StaticFiles
from pydantic import BaseModel
import xml.etree.ElementTree as ET

# 加载 .env 文件
load_dotenv()

# 添加加解密库路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'weworkapi_python', 'callback'))

try:
    from weworkapi_python.callback.WXBizMsgCrypt3 import WXBizMsgCrypt
except ImportError:
    from weworkapi_python.callback.WXBizMsgCrypt import WXBizMsgCrypt

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 减少uvicorn的日志级别以避免过多的无效请求警告
uvicorn_logger = logging.getLogger("uvicorn.access")
uvicorn_logger.setLevel(logging.WARNING)

# 创建 FastAPI 应用
app = FastAPI(
    title="企业微信回调API服务",
    description="基于FastAPI框架实现企业微信消息接收和处理",
    version="2.0.0",
    docs_url=None  # 禁用默认文档，使用自定义文档
)

# 添加中间件处理无效请求
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import status
from starlette.responses import Response

class InvalidRequestMiddleware(BaseHTTPMiddleware):
    """处理无效HTTP请求的中间件"""

    async def dispatch(self, request, call_next):
        try:
            # 检查请求路径是否包含常见的恶意扫描路径
            malicious_paths = [
                '/wp-admin', '/admin', '/.env', '/config',
                '/phpMyAdmin', '/mysql', '/phpmyadmin',
                '/wp-login.php', '/wp-config.php', '/.git',
                '/robots.txt', '/sitemap.xml'
            ]

            if any(path in str(request.url.path).lower() for path in malicious_paths):
                logger.warning(f"阻止恶意扫描请求: {request.url.path} from {request.client.host if request.client else 'unknown'}")
                return Response(status_code=status.HTTP_404_NOT_FOUND)

            response = await call_next(request)
            return response
        except Exception as e:
            logger.error(f"请求处理异常: {e}")
            return Response(status_code=status.HTTP_400_BAD_REQUEST)

# 添加中间件
app.add_middleware(InvalidRequestMiddleware)

# 挂载静态文件路径（如果存在）
if os.path.exists("static"):
    app.mount("/static", StaticFiles(directory="static"), name="static")

# Pydantic 模型
class GroupListParams(BaseModel):
    """群聊列表查询参数"""
    limit: int = 100
    cursor: Optional[str] = None
    status_filter: int = 0

# Webhook 消息模型
class TextMessage(BaseModel):
    """文本消息"""
    content: str
    mentioned_list: Optional[list] = None
    mentioned_mobile_list: Optional[list] = None

class MarkdownMessage(BaseModel):
    """Markdown消息"""
    content: str

class ImageMessage(BaseModel):
    """图片消息"""
    base64: str
    md5: str

class NewsArticle(BaseModel):
    """图文消息文章"""
    title: str
    description: Optional[str] = None
    url: str
    picurl: Optional[str] = None

class NewsMessage(BaseModel):
    """图文消息"""
    articles: List[NewsArticle]

class FileMessage(BaseModel):
    """文件消息"""
    media_id: str

class VoiceMessage(BaseModel):
    """语音消息"""
    media_id: str

class TemplateCardMessage(BaseModel):
    """模板卡片消息"""
    card_type: str
    source: Optional[dict] = None
    main_title: Optional[dict] = None
    emphasis_content: Optional[dict] = None
    quote_area: Optional[dict] = None
    sub_title_text: Optional[str] = None
    horizontal_content_list: Optional[list] = None
    jump_list: Optional[list] = None
    card_action: Optional[dict] = None

class WeComCallbackConfig:
    """企业微信回调配置"""
    def __init__(self):
        # 从环境变量读取配置
        self.TOKEN = os.getenv('WECOM_TOKEN', 'your_token_here')
        self.ENCODING_AES_KEY = os.getenv('WECOM_ENCODING_AES_KEY', 'your_encoding_aes_key_here')
        self.CORP_ID = os.getenv('WECOM_CORP_ID', 'your_corp_id_here')

        # 企业微信API配置
        self.APP_SECRET = os.getenv('WECOM_APP_SECRET', 'tXO7PS7EdjIyjPGAZOHF8qkw5URgWa8GjwG9m70IUsw')

        # 企业微信 Webhook 配置
        self.WEBHOOK_KEY = os.getenv('WECOM_WEBHOOK_KEY', 'your_webhook_key_here')

        # 验证配置
        if any(v == f'your_{k.lower()}_here' for k, v in self.__dict__.items()):
            logger.warning("请设置正确的企业微信配置参数")

config = WeComCallbackConfig()

class WeComMessageHandler:
    """企业微信消息处理器"""

    def __init__(self):
        # 检查配置是否有效
        if self._is_config_valid():
            try:
                self.wxcpt = WXBizMsgCrypt(config.TOKEN, config.ENCODING_AES_KEY, config.CORP_ID)
                logger.info("企业微信加解密组件初始化成功")
            except Exception as e:
                logger.error(f"企业微信加解密组件初始化失败: {e}")
                self.wxcpt = None
        else:
            logger.warning("企业微信配置无效，请设置正确的配置参数")
            self.wxcpt = None

        # 尝试导入自定义消息处理器
        try:
            from message_handlers import custom_handler
            self.custom_handler = custom_handler
            logger.info("已加载自定义消息处理器")
        except ImportError:
            logger.info("未找到自定义消息处理器，使用默认处理器")
            self.custom_handler = None

    def _is_config_valid(self):
        """检查配置是否有效"""
        required_configs = [config.TOKEN, config.ENCODING_AES_KEY, config.CORP_ID]
        default_values = ['your_token_here', 'your_encoding_aes_key_here', 'your_corp_id_here']

        for cfg, default in zip(required_configs, default_values):
            if not cfg or cfg == default:
                return False

        # 检查 EncodingAESKey 长度
        if len(config.ENCODING_AES_KEY) != 43:
            return False

        return True

    def handle_text_message(self, msg_data):
        """处理文本消息"""
        try:
            # 优先使用自定义处理器
            if self.custom_handler:
                return self.custom_handler.handle_text_message(msg_data)

            # 默认处理逻辑
            xml_tree = ET.fromstring(msg_data)
            from_user = xml_tree.find("FromUserName").text
            content = xml_tree.find("Content").text

            # 检查是否是群聊消息
            chat_id = xml_tree.find("ChatId")
            is_group_chat = chat_id is not None

            if is_group_chat:
                chat_id_value = chat_id.text
                logger.info(f"收到群聊文本消息 - 群聊ID: {chat_id_value}, 发送者: {from_user}, 内容: {content}")

                # 简单的群聊回复逻辑（使用智能触发判断）
                if self.should_reply_in_group_aibot(content):
                    if "你好" in content:
                        reply_content = f"@{from_user} 你好！有什么可以帮助您的吗？"
                    elif "时间" in content:
                        reply_content = f"@{from_user} 当前时间：{time.strftime('%Y-%m-%d %H:%M:%S')}"
                    else:
                        reply_content = f"@{from_user} 收到您的消息：{content}"

                    reply_msg = self.create_group_reply(chat_id_value, reply_content)
                    return reply_msg
                else:
                    # 不需要回复的群聊消息
                    return None
            else:
                # 私聊消息处理
                logger.info(f"收到私聊文本消息 - 用户: {from_user}, 内容: {content}")

                # 简单的回复逻辑
                if "你好" in content:
                    reply_content = "你好！有什么可以帮助您的吗？"
                elif "时间" in content:
                    reply_content = f"当前时间：{time.strftime('%Y-%m-%d %H:%M:%S')}"
                else:
                    reply_content = f"收到您的消息：{content}"

                reply_msg = self.create_text_reply(from_user, reply_content)
                return reply_msg

        except Exception as e:
            logger.error(f"处理文本消息失败: {e}")
            return None

    def handle_event_message(self, msg_data):
        """处理事件消息"""
        try:
            # 优先使用自定义处理器
            if self.custom_handler:
                return self.custom_handler.handle_event_message(msg_data)

            # 默认处理逻辑
            xml_tree = ET.fromstring(msg_data)
            from_user = xml_tree.find("FromUserName").text
            event = xml_tree.find("Event").text

            logger.info(f"收到事件消息 - 用户: {from_user}, 事件: {event}")

            if event == "subscribe":
                reply_content = "欢迎关注！"
            elif event == "unsubscribe":
                reply_content = "感谢使用！"
            else:
                reply_content = f"收到事件：{event}"

            reply_msg = self.create_text_reply(from_user, reply_content)
            return reply_msg

        except Exception as e:
            logger.error(f"处理事件消息失败: {e}")
            return None

    def create_text_reply(self, to_user, content, msg_id=None):
        """创建私聊文本回复消息"""
        timestamp = str(int(time.time()))

        reply_xml = f"""<xml>
<ToUserName><![CDATA[{to_user}]]></ToUserName>
<FromUserName><![CDATA[企业微信助手]]></FromUserName>
<CreateTime>{timestamp}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[{content}]]></Content>
</xml>"""

        return reply_xml

    def create_group_reply(self, chat_id, content, msg_id=None):
        """创建群聊文本回复消息"""
        timestamp = str(int(time.time()))

        reply_xml = f"""<xml>
<ChatId><![CDATA[{chat_id}]]></ChatId>
<FromUserName><![CDATA[企业微信助手]]></FromUserName>
<CreateTime>{timestamp}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[{content}]]></Content>
</xml>"""

        return reply_xml

    def process_message(self, msg_data):
        """处理消息的主入口"""
        try:
            # 检查是否是智能机器人的JSON格式消息
            if msg_data.strip().startswith('{'):
                return self.process_aibot_message(msg_data)

            # 传统XML格式消息
            xml_tree = ET.fromstring(msg_data)
            msg_type = xml_tree.find("MsgType").text

            logger.info(f"消息类型: {msg_type}")

            if msg_type == "text":
                return self.handle_text_message(msg_data)
            elif msg_type == "event":
                return self.handle_event_message(msg_data)
            else:
                logger.info(f"暂不支持的消息类型: {msg_type}")
                return None

        except Exception as e:
            logger.error(f"处理消息失败: {e}")
            return None

    def process_aibot_message(self, msg_data):
        """处理智能机器人JSON格式消息"""
        try:
            import json
            msg_json = json.loads(msg_data)

            logger.info(f"智能机器人消息: {msg_json}")

            msg_type = msg_json.get('msgtype')
            from_user = msg_json.get('from', {}).get('userid', 'unknown')
            msgid = msg_json.get('msgid', '')

            # 检查是否是群聊消息
            chat_id = msg_json.get('chat_id', '')
            is_group_chat = bool(chat_id)

            if msg_type == 'text':
                content = msg_json.get('text', {}).get('content', '')

                if is_group_chat:
                    logger.info(f"收到智能机器人群聊文本消息 - 群聊ID: {chat_id}, 用户: {from_user}, 内容: {content}")

                    # 检查是否需要在群聊中回复
                    if self.should_reply_in_group_aibot(content):
                        reply_content = self.generate_group_smart_reply(content, from_user)
                    else:
                        # 不需要回复
                        return None
                else:
                    logger.info(f"收到智能机器人私聊文本消息 - 用户: {from_user}, 内容: {content}")
                    reply_content = self.generate_smart_reply(content)

                # 按照文档 101031 创建 stream JSON 格式
                logger.info("创建 stream JSON 格式回复...")

                reply_json = {
                    "msgtype": "stream",
                    "stream": {
                        "id": msgid,  # 使用原消息ID
                        "finish": True,
                        "content": reply_content
                    }
                }

                logger.info(f"准备发送 stream 回复: {reply_json}")
                return json.dumps(reply_json, ensure_ascii=False)
            else:
                logger.info(f"暂不支持的智能机器人消息类型: {msg_type}")
                # 对于不支持的消息类型，返回提示信息
                reply_json = {
                    "msgtype": "stream",
                    "stream": {
                        "id": msgid,
                        "finish": True,
                        "content": "抱歉，我暂时只能处理文本消息。"
                    }
                }
                return json.dumps(reply_json, ensure_ascii=False)

        except Exception as e:
            logger.error(f"处理智能机器人消息失败: {e}")
            return None

    def should_reply_in_group_aibot(self, content):
        """判断是否需要在群聊中回复（智能机器人模式）"""
        # 检查是否@机器人
        if "@机器人" in content or "@bot" in content or "@助手" in content:
            return True

        # 检查命令
        if content.startswith('/') or content.startswith('！'):
            return True

        # 检查直接询问关键词（更精确的匹配）
        content_lower = content.lower().strip()

        # 直接的帮助请求
        if content_lower in ["帮助", "help", "？", "?", "命令", "功能"]:
            return True

        # 时间查询
        if any(phrase in content_lower for phrase in ["现在几点", "什么时间", "当前时间", "几点了"]):
            return True

        # 天气查询（更精确）
        if any(phrase in content_lower for phrase in ["天气怎么样", "天气如何", "今天天气怎么样", "查询天气", "天气预报"]):
            return True

        # 其他查询请求
        if content_lower.startswith(("查询", "请问", "问一下", "想知道")):
            return True

        return False

    def generate_group_smart_reply(self, content, from_user):
        """生成群聊智能回复内容"""
        # 移除@机器人的部分
        clean_content = content.replace("@机器人", "").replace("@bot", "").replace("@助手", "").strip()

        # 生成基础回复
        base_reply = self.generate_smart_reply(clean_content)

        # 在群聊中添加@用户
        return f"@{from_user} {base_reply}"

    def generate_smart_reply(self, content):
        """生成智能回复内容"""
        content = content.strip().lower()

        # 问候语
        if any(word in content for word in ["你好", "hi", "hello", "您好"]):
            return "你好！我是智能助手，有什么可以帮助您的吗？"

        # 时间查询
        elif any(word in content for word in ["时间", "几点", "现在"]):
            import time
            current_time = time.strftime('%Y年%m月%d日 %H:%M:%S')
            return f"当前时间：{current_time}"

        # 日期查询
        elif any(word in content for word in ["日期", "今天", "几号"]):
            import time
            current_date = time.strftime('%Y年%m月%d日 星期%w')
            weekdays = ['日', '一', '二', '三', '四', '五', '六']
            weekday = weekdays[int(time.strftime('%w'))]
            current_date = current_date.replace('星期%w', f'星期{weekday}')
            return f"今天是：{current_date}"

        # 帮助信息
        elif any(word in content for word in ["帮助", "help", "功能", "能做什么"]):
            return (
                "我可以帮您：\n"
                "- 查询时间日期\n"
                "- 简单对话交流\n"
                "- 回答常见问题\n"
                "- 记录简单信息\n\n"
                "您可以直接向我提问！"
            )

        # 感谢
        elif any(word in content for word in ["谢谢", "感谢", "thanks"]):
            return "不客气！很高兴能帮助您！"

        # 再见
        elif any(word in content for word in ["再见", "拜拜", "bye", "goodbye"]):
            return "再见！有需要随时找我！"

        # 默认回复
        else:
            return f"收到您的消息：{content}\n\n我正在学习中，如需帮助请输入\"帮助\"查看功能列表。"

# 创建消息处理器实例
message_handler = WeComMessageHandler()

# 企业微信API相关函数
def get_access_token():
    """获取企业微信访问令牌"""
    url = f'https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={config.CORP_ID}&corpsecret={config.APP_SECRET}'
    try:
        response = requests.get(url)
        data = response.json()
        if 'access_token' in data:
            return data['access_token']
        else:
            logger.error(f"获取ACCESS_TOKEN失败: {data}")
            raise HTTPException(status_code=500, detail=f"获取ACCESS_TOKEN失败: {data.get('errmsg', '未知错误')}")
    except requests.RequestException as e:
        logger.error(f"请求ACCESS_TOKEN异常: {e}")
        raise HTTPException(status_code=500, detail="网络请求失败")



@app.get("/wecom/callback")
async def wecom_callback_get(
    msg_signature: str = Query(..., description="消息签名"),
    timestamp: str = Query(..., description="时间戳"),
    nonce: str = Query(..., description="随机数"),
    echostr: str = Query(..., description="回显字符串")
):
    """企业微信URL验证"""
    try:
        logger.info(f"URL验证请求 - signature: {msg_signature}, timestamp: {timestamp}, nonce: {nonce}")

        # 检查配置是否有效
        if not message_handler.wxcpt:
            logger.error("企业微信配置无效，无法进行URL验证")
            raise HTTPException(status_code=403, detail="配置无效")

        ret, reply_echostr = message_handler.wxcpt.VerifyURL(msg_signature, timestamp, nonce, echostr)

        if ret != 0:
            logger.error(f"URL验证失败，错误码: {ret}")
            raise HTTPException(status_code=403, detail="验证失败")

        logger.info("URL验证成功")
        return PlainTextResponse(reply_echostr)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"URL验证异常: {e}")
        raise HTTPException(status_code=500, detail="验证异常")

@app.post("/wecom/callback")
async def wecom_callback_post(
    request: Request,
    msg_signature: str = Query(..., description="消息签名"),
    timestamp: str = Query(..., description="时间戳"),
    nonce: str = Query(..., description="随机数")
):
    """企业微信消息处理"""
    try:
        # 获取POST数据
        post_data = await request.body()

        logger.info(f"收到消息 - signature: {msg_signature}, timestamp: {timestamp}, nonce: {nonce}")

        # 检查配置是否有效
        if not message_handler.wxcpt:
            logger.error("企业微信配置无效，无法处理消息")
            return PlainTextResponse("企业微信配置无效，无法处理消息")  # 返回success避免重复推送

        # 解密消息
        ret, msg_data = message_handler.wxcpt.DecryptMsg(post_data, msg_signature, timestamp, nonce)

        if ret != 0:
            logger.error(f"消息解密失败，错误码: {ret}")

            # 如果是智能机器人的 XML 解析失败，尝试手动解密
            if ret == -40002:  # XML解析失败
                try:
                    logger.info("尝试智能机器人模式解密消息...")
                    logger.info(f"POST数据长度: {len(post_data)}")
                    logger.info(f"POST数据内容: {post_data[:200]}...")

                    import xml.etree.ElementTree as ET
                    import base64
                    from Crypto.Cipher import AES
                    import socket
                    import struct

                    # 尝试解析POST数据
                    try:
                        post_data_str = post_data.decode('utf-8')
                        logger.info(f"POST数据字符串: {post_data_str[:200]}...")

                        # 检查是否是JSON格式（智能机器人）
                        if post_data_str.startswith('{'):
                            import json, hashlib
                            json_data = json.loads(post_data_str)
                            encrypt_msg = json_data.get('encrypt')
                            if not encrypt_msg:
                                logger.error("JSON数据中没有找到encrypt字段")
                                return PlainTextResponse("success")
                            # 按 101033 校验签名：sha1(token, timestamp, nonce, encrypt)
                            raw_list = [config.TOKEN, timestamp, nonce, encrypt_msg]
                            raw_list.sort()
                            sha = hashlib.sha1()
                            sha.update(''.join(raw_list).encode('utf-8'))
                            calc_sig = sha.hexdigest()
                            if calc_sig != msg_signature:
                                logger.error(f"签名校验失败，calc={calc_sig}, req={msg_signature}")
                                return PlainTextResponse("success")
                            logger.info(f"从JSON提取到加密消息: {encrypt_msg[:50]}...")
                        else:
                            # 尝试XML格式解析
                            root = ET.fromstring(post_data_str)
                            encrypt_msg = root.find('Encrypt').text
                            logger.info(f"从XML提取到加密消息: {encrypt_msg[:50]}...")

                    except Exception as parse_e:
                        logger.error(f"数据解析失败: {parse_e}")
                        return PlainTextResponse("success")

                    # 解码 AES Key
                    key = None
                    for padding in ['', '=', '==', '===']:
                        try:
                            key = base64.b64decode(config.ENCODING_AES_KEY + padding)
                            if len(key) == 32:
                                break
                        except:
                            continue

                    if key is None:
                        logger.error("AES Key 解码失败")
                        return PlainTextResponse("success")

                    # 解密
                    cryptor = AES.new(key, AES.MODE_CBC, key[:16])
                    plain_text = cryptor.decrypt(base64.b64decode(encrypt_msg))

                    pad = plain_text[-1]
                    content = plain_text[16:-pad]

                    if len(content) < 4:
                        logger.error("解密内容太短")
                        return PlainTextResponse("success")

                    xml_len = socket.ntohl(struct.unpack("I", content[:4])[0])

                    if len(content) < 4 + xml_len:
                        logger.error("内容长度不足")
                        return PlainTextResponse("success")

                    xml_content = content[4:4 + xml_len]
                    msg_data = xml_content.decode('utf-8')

                    logger.info(f"智能机器人模式解密成功，消息内容: {msg_data}")

                except Exception as e:
                    logger.error(f"智能机器人模式解密失败: {e}")
                    return PlainTextResponse("success")
            else:
                return PlainTextResponse("success")

        logger.info(f"解密成功，消息内容: {msg_data}")

        # 处理消息
        reply_msg = message_handler.process_message(msg_data)

        if reply_msg:
            # 检查是否是智能机器人的JSON格式消息
            if msg_data.strip().startswith('{'):
                # 智能机器人模式，按照截图代码逻辑处理
                logger.info(f"智能机器人回复: {reply_msg}")
                logger.info("发送智能机器人回复消息")

                # 7. 加密回复 - 按照截图代码逻辑
                # 使用现有的 wxcpt 对象进行加密
                ret, encrypt_result = message_handler.wxcpt.EncryptMsg(reply_msg, nonce, timestamp)

                if ret != 0:
                    logger.error(f"智能机器人回复加密失败，错误码: {ret}")
                    return PlainTextResponse("success")

                # 从加密结果中提取 encrypt 字段（XML格式）
                import xml.etree.ElementTree as ET
                try:
                    root = ET.fromstring(encrypt_result)
                    encrypt = root.find('Encrypt').text
                    msg_signature = root.find('MsgSignature').text
                    timestamp_from_xml = root.find('TimeStamp').text
                    nonce_from_xml = root.find('Nonce').text

                    # 构造最终响应（按照截图格式）
                    import json
                    response = {
                        "encrypt": encrypt,
                        "msg_signature": msg_signature,
                        "timestamp": timestamp_from_xml,
                        "nonce": nonce_from_xml
                    }

                    logger.info(f"最终加密响应: {response}")

                    from fastapi.responses import Response
                    return Response(
                        content=json.dumps(response, ensure_ascii=False),
                        media_type="application/json; charset=utf-8"
                    )
                except Exception as e:
                    logger.error(f"解析加密结果失败: {e}")
                    logger.error(f"加密结果内容: {encrypt_result}")
                    return PlainTextResponse("success")
            else:
                # 传统XML消息，加密回复
                ret, encrypted_msg = message_handler.wxcpt.EncryptMsg(reply_msg, nonce, timestamp)

                if ret != 0:
                    logger.error(f"消息加密失败，错误码: {ret}")
                    return PlainTextResponse("success")

                logger.info("回复消息发送成功")
                return PlainTextResponse(encrypted_msg, media_type="application/xml; charset=utf-8")

        return PlainTextResponse("success")

    except Exception as e:
        logger.error(f"处理消息异常: {e}")
        return PlainTextResponse(f"处理消息异常: {e}")  # 返回success避免重复推送

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "ok",
        "timestamp": int(time.time()),
        "service": "wecom-fastapi-server",
        "framework": "FastAPI"
    }

@app.get("/config")
async def get_config():
    """获取配置信息（不包含敏感信息）"""
    return {
        "corp_id": config.CORP_ID,
        "token_configured": bool(config.TOKEN and config.TOKEN != 'your_token_here'),
        "aes_key_configured": bool(config.ENCODING_AES_KEY and config.ENCODING_AES_KEY != 'your_encoding_aes_key_here'),
        "framework": "FastAPI"
    }

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "企业微信回调API服务 - FastAPI版本",
        "version": "2.0.0",
        "docs": "/docs",
        "health": "/health"
    }

@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """自定义Swagger UI文档"""
    if os.path.exists("static"):
        return get_swagger_ui_html(
            openapi_url=app.openapi_url,
            title="企业微信API文档",
            swagger_js_url="/static/swagger-ui-bundle.js",
            swagger_css_url="/static/swagger-ui.css"
        )
    else:
        # 如果没有静态文件，使用默认CDN
        return get_swagger_ui_html(
            openapi_url=app.openapi_url,
            title="企业微信API文档"
        )

@app.get("/access_token")
async def get_access_token_endpoint():
    """获取企业微信访问令牌"""
    try:
        access_token = get_access_token()
        return {"access_token": access_token}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取访问令牌异常: {e}")
        raise HTTPException(status_code=500, detail="获取访问令牌失败")


@app.get('/WW_verify_eNFsvK7nLEz9Gbb7.txt')
async def serve_verify_file():
    """企业微信域名验证文件"""
    # 获取当前文件所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 拼接验证文件的完整路径
    file_path = os.path.join(current_dir, "WW_verify_eNFsvK7nLEz9Gbb7.txt")

    # 检查文件是否存在
    if os.path.exists(file_path):
        return FileResponse(file_path)
    else:
        logger.warning(f"验证文件不存在: {file_path}")
        raise HTTPException(status_code=404, detail="验证文件不存在")


if __name__ == '__main__':
    import uvicorn

    # 检查配置
    if not all([config.TOKEN, config.ENCODING_AES_KEY, config.CORP_ID]):
        logger.error("请设置环境变量: WECOM_TOKEN, WECOM_ENCODING_AES_KEY, WECOM_CORP_ID")
        sys.exit(1)

    # 启动服务
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('DEBUG', 'False').lower() == 'true'

    logger.info(f"启动企业微信回调服务（FastAPI版本），端口: {port}")
    logger.info(f"服务地址: http://0.0.0.0:{port}")
    logger.info(f"回调地址: http://0.0.0.0:{port}/wecom/callback")
    logger.info(f"API文档: http://0.0.0.0:{port}/docs")

    try:
        uvicorn.run(
            "wecom_fastapi_server:app",
            host="0.0.0.0",
            port=port,
            reload=debug,
            log_level="warning",  # 减少日志噪音
            access_log=False      # 禁用访问日志以减少无效请求的日志
        )
    except KeyboardInterrupt:
        logger.info("服务已停止")
    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        sys.exit(1)
